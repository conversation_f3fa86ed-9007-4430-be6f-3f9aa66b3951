<div class="ajax-search-container">
    <form role="search" method="get" class="search-form" action="<?php echo esc_url( home_url( '/' ) ); ?>">
        <div class="search-input-wrapper">
            <input type="search" class="search-field" id="ajax-search-input" placeholder="<?php echo esc_attr_x( '<PERSON><PERSON><PERSON><PERSON>...', 'placeholder', 'dmrthema' ); ?>" value="<?php echo get_search_query(); ?>" name="s" autocomplete="off" />
            <input type="hidden" name="post_type" value="product" />

            <!-- Search icon -->
            <button type="submit" class="search-icon-btn">
                <i class="fas fa-search"></i>
            </button>

            <!-- Loading indicator -->
            <div class="ajax-search-loading" id="ajax-search-loading">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
        </div>
    </form>

    <!-- Search results dropdown -->
    <div class="ajax-search-results" id="ajax-search-results">
        <div class="ajax-search-results-inner">
            <!-- Results will be populated here -->
        </div>
    </div>
</div>
