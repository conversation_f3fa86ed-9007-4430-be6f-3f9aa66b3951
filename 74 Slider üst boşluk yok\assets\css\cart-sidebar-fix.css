/*
 * Cart Sidebar Fix CSS
 * Bu dosya sidebar'in tum sayfalarda tutarli gorunmesi icin olusturulmustur
 * En yuksek CSS onceligi ile tum cakismalari onler
 */

/* KRITIK: Tum sayfalarda sidebar tutarliligi */
html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.home #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.shop #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.single-product #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.archive #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-shop #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-page #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
    padding: 12px 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    display: flex !important;
    align-items: flex-start !important;
    position: relative !important;
    z-index: 1 !important;
    box-sizing: border-box !important;
}

/* Mini cart listesi */
html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart {
    padding: 0 !important;
    margin: 0 !important;
    list-style: none !important;
    flex: 1 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    max-height: calc(100vh - 320px) !important; /* Header, footer ve padding icin alan */
    padding-bottom: 20px !important;

}



/* Son elementin border'ini kaldir */
html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item:last-child {
    border-bottom: none !important;
}

/* Urun resimleri */
html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item img {
    width: 50px !important;
    height: 50px !important;
    object-fit: cover !important;
    border-radius: 5px !important;
    margin-right: 12px !important;
    flex-shrink: 0 !important;
}

/* Urun detaylari */
html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-details {
    flex: 1 !important;
    padding-right: 25px !important;
}

/* Urun adi */
html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name {
    display: block !important;
    text-decoration: none !important;
    color: #333 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
    line-height: 1.3 !important;
}

html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name:hover {
    color: #ff6000 !important;
}

/* Urun bilgileri */
html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-info {
    margin-top: 5px !important;
}

/* Miktar */
html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .quantity {
    color: #666 !important;
    font-size: 12px !important;
    display: block !important;
}

/* Remove butonu */
html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .remove {
    position: absolute !important;
    top: 10px !important;
    right: 0 !important;
    color: #ff4444 !important;
    text-decoration: none !important;
    font-weight: bold !important;
    font-size: 16px !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    background: rgba(255, 68, 68, 0.1) !important;
}

html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .remove:hover {
    color: #cc0000 !important;
    background: rgba(204, 0, 0, 0.1) !important;
}

/* Mobil responsive */
@media (max-width: 768px) {
    html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item img {
        width: 40px !important;
        height: 40px !important;
        margin-right: 10px !important;
    }

    html body #cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
        padding: 12px 0 !important;
    }
}
